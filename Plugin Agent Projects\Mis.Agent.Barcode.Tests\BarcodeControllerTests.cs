using Microsoft.AspNetCore.Mvc;
using Mis.Agent.Barcode;
using Mis.Shared.Interface;
using Moq;
using Xunit;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode.Tests
{
    public class BarcodeControllerTests
    {
        private readonly Mock<IBarcodeAppService> _mockBarcodeService;
        private readonly Mock<IScannerAppService> _mockScannerService;
        private readonly BarcodeController _controller;

        public BarcodeControllerTests()
        {
            _mockBarcodeService = new Mock<IBarcodeAppService>();
            _mockScannerService = new Mock<IScannerAppService>();
            _controller = new BarcodeController(_mockBarcodeService.Object, _mockScannerService.Object);
        }

        [Fact]
        public async Task ScanAsync_WhenIsScanByBarcodeReaderTrue_ShouldUseTwainScanning()
        {
            // Arrange
            _mockScannerService.Setup(x => x.GetSetting("IsScanByBarcodeReader"))
                              .Returns("true");

            // Act
            var result = await _controller.ScanAsync();

            // Assert
            Assert.NotNull(result);
            // Note: This test will likely fail in CI/CD without actual TWAIN devices
            // but it verifies the code path and structure
        }

        [Fact]
        public async Task ScanAsync_WhenIsScanByBarcodeReaderFalse_ShouldUseScannerDevice()
        {
            // Arrange
            _mockScannerService.Setup(x => x.GetSetting("IsScanByBarcodeReader"))
                              .Returns("false");
            _mockScannerService.Setup(x => x.GetSetting("Scanner"))
                              .Returns("TestScanner");
            _mockScannerService.Setup(x => x.GetAvailableScanners())
                              .Returns(new[] { "TestScanner" });

            // Act
            var result = await _controller.ScanAsync();

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task ScanAsync_WhenExceptionOccurs_ShouldReturnErrorResponse()
        {
            // Arrange
            _mockScannerService.Setup(x => x.GetSetting("IsScanByBarcodeReader"))
                              .Throws(new System.Exception("Test exception"));

            // Act
            var result = await _controller.ScanAsync();

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }
    }
}
