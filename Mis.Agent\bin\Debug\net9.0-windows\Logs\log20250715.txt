2025-07-15 16:45:26.296 +03:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-07-15 16:45:26.727 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:45:26.946 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:45:26.954 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:45:26.968 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:45:26.976 +03:00 [INF] Hosting environment: Production
2025-07-15 16:45:26.979 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:46:26.270 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:46:26.398 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:46:26.411 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:46:26.424 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:46:26.427 +03:00 [INF] Initializing hub connection...
2025-07-15 16:46:57.161 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:46:57.254 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:46:57.268 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:46:57.285 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:46:57.289 +03:00 [INF] Initializing hub connection...
2025-07-15 16:46:59.359 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:46:59.445 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:46:59.450 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:46:59.455 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:46:59.457 +03:00 [INF] Hosting environment: Production
2025-07-15 16:46:59.459 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:46:59.768 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:46:59.809 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:46:59.833 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:46:59.837 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 74.3389ms
2025-07-15 16:47:01.980 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=PRNaehZfw1a9-OcCzQZgAg - null null
2025-07-15 16:47:01.994 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:47:02.066 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:47:02.147 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:47:08.221 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/ - null null
2025-07-15 16:47:08.256 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/ - 301 0 null 34.6323ms
2025-07-15 16:47:08.267 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-15 16:47:08.311 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 43.5083ms
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-15 16:47:08.347 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-15 16:47:08.376 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-15 16:47:08.380 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 32.4567ms
2025-07-15 16:47:08.382 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 35.0573ms
2025-07-15 16:47:08.449 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-15 16:47:08.449 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-15 16:47:08.454 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 106.2681ms
2025-07-15 16:47:08.454 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 106.6712ms
2025-07-15 16:47:08.458 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-15 16:47:08.466 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 119.0447ms
2025-07-15 16:47:08.688 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-15 16:47:08.804 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 116.3642ms
2025-07-15 16:47:16.098 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - null null
2025-07-15 16:47:16.110 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:47:16.128 +03:00 [INF] Route matched with {action = "CaptureImageTWAIN", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageTWAIN() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:47:16.545 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:47:33.160 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - null null
2025-07-15 16:47:33.166 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:47:33.170 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:47:33.184 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:47:33.190 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:47:33.193 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:47:33.195 +03:00 [INF] Initializing hub connection...
2025-07-15 16:47:35.237 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:47:35.253 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:47:35.256 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:47:35.281 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 44.799ms
2025-07-15 16:47:37.316 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=7cWUrw5aRYYpgV809o08cA - null null
2025-07-15 16:47:37.323 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:47:37.343 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:47:47.045 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 13854.5932ms.
2025-07-15 16:47:47.055 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:47:47.076 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) in 13900.9512ms
2025-07-15 16:47:47.078 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:47:47.079 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - 400 null application/json; charset=utf-8 13918.6837ms
2025-07-15 16:47:48.310 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:47:48.317 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:47:48.319 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:47:48.321 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.1961ms
2025-07-15 16:47:50.373 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=AoAGzKeMZ_kHrgjKiyTYRQ - null null
2025-07-15 16:47:50.380 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:47:51.092 +03:00 [INF] Connection id "0HNE3KO96IF8H", Request id "0HNE3KO96IF8H:00000001": the application aborted the connection.
2025-07-15 16:47:51.101 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 16:47:51.103 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=7cWUrw5aRYYpgV809o08cA - 101 null null 13787.1123ms
2025-07-15 16:48:24.459 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 16:48:24.464 +03:00 [INF] COM ports populated.
2025-07-15 16:48:24.490 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 16:48:24.491 +03:00 [INF] TabPage returned successfully.
2025-07-15 16:49:29.882 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:49:30.013 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:49:30.028 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:49:30.043 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:49:30.047 +03:00 [INF] Initializing hub connection...
2025-07-15 16:49:32.861 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:49:32.945 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:49:32.950 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:49:32.957 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:49:32.961 +03:00 [INF] Hosting environment: Production
2025-07-15 16:49:32.964 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:49:33.217 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:49:33.273 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:49:33.302 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:49:33.308 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 99.8286ms
2025-07-15 16:49:35.469 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=nQUU_wKbBUOehEcN4oa-mg - null null
2025-07-15 16:49:35.483 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:49:35.622 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:49:35.770 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:51:00.727 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:51:00.840 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:51:00.853 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:51:00.868 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:51:00.871 +03:00 [INF] Initializing hub connection...
2025-07-15 16:51:03.371 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:51:03.479 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:51:03.484 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:51:03.491 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:51:03.494 +03:00 [INF] Hosting environment: Production
2025-07-15 16:51:03.496 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:51:03.953 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:51:04.002 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:51:04.034 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:51:04.040 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 94.6402ms
2025-07-15 16:51:06.233 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=VQN11CdkINRC9fkHHJWUng - null null
2025-07-15 16:51:06.242 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:51:06.355 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:51:06.453 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:51:59.986 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:52:00.089 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:52:00.103 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:52:00.115 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:52:00.119 +03:00 [INF] Initializing hub connection...
2025-07-15 16:52:03.001 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:52:03.097 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:52:03.106 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:52:03.112 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:52:03.114 +03:00 [INF] Hosting environment: Production
2025-07-15 16:52:03.116 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:52:03.350 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:52:03.391 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:52:03.420 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:52:03.428 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 86.0135ms
2025-07-15 16:52:05.613 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=U8_0vhGOeTivgB2bWrHUig - null null
2025-07-15 16:52:05.620 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:52:05.738 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:52:05.826 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:52:11.859 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:52:11.868 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:52:11.872 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 13.6878ms
2025-07-15 16:52:11.877 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:52:11.884 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:52:11.886 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:52:11.907 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:52:12.310 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:52:12.316 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:52:12.318 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:52:12.319 +03:00 [INF] Initializing hub connection...
2025-07-15 16:52:14.363 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:52:14.371 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:52:14.373 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:52:14.405 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42.4437ms
2025-07-15 16:52:16.424 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ydqMUPr9C2XfpPy7X8GPsg - null null
2025-07-15 16:52:16.432 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:52:42.248 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:52:42.256 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:52:42.258 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.8158ms
2025-07-15 16:53:02.265 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:53:02.271 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:53:02.272 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:53:02.274 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:53:02.291 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:53:02.294 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:53:02.297 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:53:02.298 +03:00 [INF] Initializing hub connection...
2025-07-15 16:53:04.353 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:04.359 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:04.360 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:04.386 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.576ms
2025-07-15 16:53:06.413 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=fvo4PWehleNTkIlWll9zkQ - null null
2025-07-15 16:53:06.418 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:53:06.426 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:53:14.369 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 12070.1294ms.
2025-07-15 16:53:14.378 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:53:14.390 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) in 12110.2709ms
2025-07-15 16:53:14.392 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:53:14.395 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 12129.636ms
2025-07-15 16:53:15.414 +03:00 [INF] Connection id "0HNE3KR3LJH8L", Request id "0HNE3KR3LJH8L:00000001": the application aborted the connection.
2025-07-15 16:53:15.425 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 16:53:15.428 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=fvo4PWehleNTkIlWll9zkQ - 101 null null 9014.9405ms
2025-07-15 16:53:15.598 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:15.605 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:15.608 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:15.632 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 34.0004ms
2025-07-15 16:53:16.318 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:53:16.321 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:53:16.323 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3432ms
2025-07-15 16:53:17.656 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mzXV-oamxxASKLlSc4IUgg - null null
2025-07-15 16:53:17.659 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:53:36.328 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:53:36.335 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:53:36.336 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode)'
2025-07-15 16:53:36.338 +03:00 [INF] Route matched with {action = "DebugBarcodeReader", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugBarcodeReader() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:53:36.361 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugBarcodeReader (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:53:36.364 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:53:36.367 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:53:36.368 +03:00 [INF] Initializing hub connection...
2025-07-15 16:53:38.407 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:38.410 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:38.412 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:38.431 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 24.3196ms
2025-07-15 16:53:46.627 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:53:46.750 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:53:46.762 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:53:46.777 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:53:46.780 +03:00 [INF] Initializing hub connection...
2025-07-15 16:53:49.033 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:53:49.196 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:53:49.202 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:53:49.211 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:53:49.215 +03:00 [INF] Hosting environment: Production
2025-07-15 16:53:49.219 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:53:49.353 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:53:49.414 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:53:49.447 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:53:49.452 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 110.4972ms
2025-07-15 16:53:51.631 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ic3KJ2QBNvxQMTx6H7Iqvw - null null
2025-07-15 16:53:51.646 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:53:51.913 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:53:52.084 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:53:57.690 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageWIA - null null
2025-07-15 16:53:57.697 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageWIA (Mis.Agent.Barcode)'
2025-07-15 16:53:57.717 +03:00 [INF] Route matched with {action = "CaptureImageWIA", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageWIA() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:53:58.158 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageWIA (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:54:49.450 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - null null
2025-07-15 16:54:49.462 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:49.467 +03:00 [INF] Route matched with {action = "CaptureImageTWAIN", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageTWAIN() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:54:49.478 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:54:49.502 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 17.1316ms.
2025-07-15 16:54:49.512 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:49.541 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) in 68.7593ms
2025-07-15 16:54:49.545 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:49.549 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - 400 null application/json; charset=utf-8 99.3043ms
2025-07-15 16:54:52.870 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - null null
2025-07-15 16:54:52.874 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:52.876 +03:00 [INF] Route matched with {action = "CaptureImageTWAIN", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CaptureImageTWAIN() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:54:52.887 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:54:52.902 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 12.7211ms.
2025-07-15 16:54:52.904 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:54:52.907 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode) in 27.2557ms
2025-07-15 16:54:52.909 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.CaptureImageTWAIN (Mis.Agent.Barcode)'
2025-07-15 16:54:52.911 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/CaptureImageTWAIN - 400 null application/json; charset=utf-8 41.1392ms
2025-07-15 16:55:10.082 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - null null
2025-07-15 16:55:10.110 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - 404 0 null 27.6523ms
2025-07-15 16:55:10.118 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader, Response status code: 404
2025-07-15 16:55:10.937 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - null null
2025-07-15 16:55:10.943 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader - 404 0 null 6.7859ms
2025-07-15 16:55:10.950 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://127.0.0.1:7000/Barcode/DebugBarcodeReader, Response status code: 404
2025-07-15 16:57:01.478 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:57:01.621 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 16:57:01.641 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 16:57:01.657 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:57:01.663 +03:00 [INF] Initializing hub connection...
2025-07-15 16:57:05.139 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:57:05.362 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:57:05.371 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:57:05.385 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:57:05.390 +03:00 [INF] Hosting environment: Production
2025-07-15 16:57:05.397 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:57:05.908 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:57:06.095 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:57:06.182 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:57:06.192 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 311.0167ms
2025-07-15 16:57:08.610 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=eYZ3NATWMzCuyyLNk5HL0w - null null
2025-07-15 16:57:08.627 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:57:08.782 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 16:57:08.867 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:57:21.542 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:57:21.550 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:57:21.554 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.479ms
2025-07-15 16:57:21.559 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:57:21.563 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:57:21.565 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:57:21.593 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:57:22.091 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:57:28.814 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 6709.8909ms.
2025-07-15 16:57:28.858 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:57:28.876 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 16:57:28.878 +03:00 [INF] COM ports populated.
2025-07-15 16:57:28.885 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 7283.3615ms
2025-07-15 16:57:28.887 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:57:28.889 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 7329.8882ms
2025-07-15 16:57:30.019 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 16:57:30.020 +03:00 [INF] TabPage returned successfully.
2025-07-15 16:57:38.232 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:57:38.275 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:57:38.281 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:57:38.285 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:57:38.287 +03:00 [INF] Initializing hub connection...
2025-07-15 16:57:39.325 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:57:39.370 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:57:39.374 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:57:39.380 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:57:39.383 +03:00 [INF] Hosting environment: Production
2025-07-15 16:57:39.385 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:57:40.438 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:57:40.473 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:57:40.491 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:57:40.496 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.3308ms
2025-07-15 16:57:42.543 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-B4EU-wEy21wMoI_5Ee1Iw - null null
2025-07-15 16:57:42.551 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:57:42.591 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:57:42.644 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:58:45.815 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:58:45.856 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:58:45.859 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 43.2838ms
2025-07-15 16:58:45.863 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:58:45.867 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:58:45.868 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:58:45.875 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:58:46.256 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:58:46.257 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:58:46.258 +03:00 [INF] Initializing hub connection...
2025-07-15 16:58:48.286 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:58:48.291 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:58:48.293 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:58:48.296 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 9.989ms
2025-07-15 16:58:48.300 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:58:48.316 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2437.2854ms
2025-07-15 16:58:48.319 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:58:48.321 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2458.1757ms
2025-07-15 16:58:50.324 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=fCT9LzS1QeK2VyV68w1nEA - null null
2025-07-15 16:58:50.335 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:13:05.374 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:13:05.470 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 17:13:05.483 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 17:13:05.494 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:13:05.498 +03:00 [INF] Initializing hub connection...
2025-07-15 17:13:07.650 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:13:07.735 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:13:07.739 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:13:07.743 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:13:07.744 +03:00 [INF] Hosting environment: Production
2025-07-15 17:13:07.746 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:13:08.065 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:13:08.105 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:13:08.130 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:13:08.136 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 77.7866ms
2025-07-15 17:13:10.324 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=dIxneAMZiWtqO3XnBKF_Pw - null null
2025-07-15 17:13:10.335 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:13:10.397 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 17:13:10.447 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:13:27.943 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:13:28.020 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 17:13:28.030 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 17:13:28.039 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:13:28.042 +03:00 [INF] Initializing hub connection...
2025-07-15 17:13:30.652 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:13:30.763 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:13:30.767 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:13:30.775 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:13:30.776 +03:00 [INF] Hosting environment: Production
2025-07-15 17:13:30.778 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:13:31.097 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:13:31.245 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:13:31.322 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:13:31.335 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 257.6365ms
2025-07-15 17:13:33.849 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FWfrBGKWoGsuEgywZ7l03w - null null
2025-07-15 17:13:33.872 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:13:34.019 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 17:13:34.085 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:13:44.075 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 17:13:44.078 +03:00 [INF] COM ports populated.
2025-07-15 17:13:45.235 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 17:13:45.242 +03:00 [INF] TabPage returned successfully.
2025-07-15 17:13:53.708 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:13:53.730 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:13:53.733 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:13:53.736 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:13:53.737 +03:00 [INF] Initializing hub connection...
2025-07-15 17:13:54.457 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:13:54.479 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:13:54.482 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:13:54.486 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:13:54.493 +03:00 [INF] Hosting environment: Production
2025-07-15 17:13:54.507 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:13:55.838 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:13:55.859 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:13:55.870 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:13:55.875 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 37.446ms
2025-07-15 17:13:57.937 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=DSZovQbM5f_C26pGVCDtwQ - null null
2025-07-15 17:13:57.944 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:13:57.970 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:13:58.026 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:14:03.092 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 17:14:03.094 +03:00 [INF] COM ports populated.
2025-07-15 17:14:03.097 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 17:14:03.099 +03:00 [INF] TabPage returned successfully.
2025-07-15 17:14:08.125 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:14:08.131 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:14:08.133 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.5851ms
2025-07-15 17:14:08.136 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:14:08.138 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:14:08.139 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:14:08.144 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:14:08.543 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:14:08.547 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:14:08.547 +03:00 [INF] Initializing hub connection...
2025-07-15 17:14:10.583 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:14:10.588 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:14:10.592 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:14:10.595 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:14:10.598 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 9.5265ms
2025-07-15 17:14:10.600 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2450.705ms
2025-07-15 17:14:10.603 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:14:10.605 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2468.9751ms
2025-07-15 17:14:12.646 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=5wSLI67SBQlOeW5OiXMQkQ - null null
2025-07-15 17:14:12.649 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:14:16.275 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:14:16.279 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:14:16.280 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3846ms
2025-07-15 17:14:16.283 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:14:16.288 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:14:16.289 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:14:16.292 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:14:16.295 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:14:16.296 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:14:16.298 +03:00 [INF] Initializing hub connection...
2025-07-15 17:14:18.332 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:14:18.334 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2040.3749ms
2025-07-15 17:14:18.336 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:14:18.337 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2054.6047ms
2025-07-15 17:14:18.356 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:14:18.359 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:14:18.361 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:14:18.363 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 7.088ms
2025-07-15 17:14:20.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=CuEQItYrza4uXcT0_cZ8MA - null null
2025-07-15 17:14:20.420 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:14:20.429 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:15:15.614 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/ - null null
2025-07-15 17:15:15.637 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/ - 301 0 null 22.6593ms
2025-07-15 17:15:15.647 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-15 17:15:15.737 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 89.6569ms
2025-07-15 17:15:15.776 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-15 17:15:15.776 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-15 17:15:15.776 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-15 17:15:15.777 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-15 17:15:15.777 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-15 17:15:15.794 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-15 17:15:15.800 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 23.0535ms
2025-07-15 17:15:15.809 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 32.2632ms
2025-07-15 17:15:15.811 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-15 17:15:15.811 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-15 17:15:15.817 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-15 17:15:15.826 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 48.6668ms
2025-07-15 17:15:15.826 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 50.0303ms
2025-07-15 17:15:15.829 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 52.8474ms
2025-07-15 17:15:16.125 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-15 17:15:16.243 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 118.1038ms
2025-07-15 17:15:20.123 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/TestBarcodeReaderTypes - null null
2025-07-15 17:15:20.131 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode)'
2025-07-15 17:15:20.135 +03:00 [INF] Route matched with {action = "TestBarcodeReaderTypes", controller = "Barcode"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult TestBarcodeReaderTypes() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:15:20.143 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`4[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType2`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]][], Mis.Agent.Barcode, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:15:20.156 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode) in 17.1592ms
2025-07-15 17:15:20.160 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode)'
2025-07-15 17:15:20.162 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/TestBarcodeReaderTypes - 200 null application/json; charset=utf-8 39.4042ms
2025-07-15 17:15:21.628 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/TestBarcodeReaderTypes - null null
2025-07-15 17:15:21.635 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode)'
2025-07-15 17:15:21.637 +03:00 [INF] Route matched with {action = "TestBarcodeReaderTypes", controller = "Barcode"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult TestBarcodeReaderTypes() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:15:21.640 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`4[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType2`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]][], Mis.Agent.Barcode, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:15:21.641 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode) in 1.9335ms
2025-07-15 17:15:21.643 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode)'
2025-07-15 17:15:21.645 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/TestBarcodeReaderTypes - 200 null application/json; charset=utf-8 17.5639ms
2025-07-15 17:16:52.780 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Barcode/SetBarcodeReaderType - application/json 15
2025-07-15 17:16:52.785 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:16:52.787 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode)'
2025-07-15 17:16:52.796 +03:00 [INF] Route matched with {action = "SetBarcodeReaderType", controller = "Barcode"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetBarcodeReaderType(System.String) on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:16:52.817 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:16:52.820 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode) in 20.1123ms
2025-07-15 17:16:52.822 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode)'
2025-07-15 17:16:52.825 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Barcode/SetBarcodeReaderType - 200 null application/json; charset=utf-8 45.2103ms
2025-07-15 17:17:15.455 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:17:15.481 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:17:15.486 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:17:15.490 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:17:15.492 +03:00 [INF] Initializing hub connection...
2025-07-15 17:17:16.588 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:17:16.634 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:17:16.638 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:17:16.641 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:17:16.643 +03:00 [INF] Hosting environment: Production
2025-07-15 17:17:16.644 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:17:17.720 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:17:17.740 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:17:17.756 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:17:17.761 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 43.7325ms
2025-07-15 17:17:19.856 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=kkAiQiLwsS5oWMKo1UqKMA - null null
2025-07-15 17:17:19.862 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:17:19.903 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:17:19.961 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:17:45.101 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:17:45.109 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:17:45.112 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.6644ms
2025-07-15 17:17:45.115 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:17:45.122 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:17:45.123 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:17:45.133 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:17:45.517 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:17:45.524 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:17:45.526 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:17:45.527 +03:00 [INF] Initializing hub connection...
2025-07-15 17:17:47.575 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:17:47.586 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:17:47.589 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:17:47.622 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 46.6009ms
2025-07-15 17:17:47.644 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2121.0205ms.
2025-07-15 17:17:47.650 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:17:47.669 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2532.158ms
2025-07-15 17:17:47.674 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:17:47.676 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2560.6704ms
2025-07-15 17:17:49.081 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:17:49.084 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:17:49.086 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:17:49.088 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:17:49.102 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:17:49.104 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:17:49.107 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:17:49.108 +03:00 [INF] Initializing hub connection...
2025-07-15 17:17:49.660 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=6_RaucS0YBqh7bN3ru5Vpw - null null
2025-07-15 17:17:49.668 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:17:51.154 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:17:51.161 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:17:51.164 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:17:51.215 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2068.7558ms.
2025-07-15 17:17:51.217 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 62.7154ms
2025-07-15 17:17:51.221 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:17:51.227 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2136.8468ms
2025-07-15 17:17:51.229 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:17:51.231 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2149.8926ms
2025-07-15 17:17:53.263 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-RNsAzOBMu_PgI-KkCPTEw - null null
2025-07-15 17:17:53.275 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:17:53.295 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:18:23.859 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:18:23.883 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:18:23.887 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:18:23.890 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:18:23.892 +03:00 [INF] Initializing hub connection...
2025-07-15 17:18:24.942 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:18:24.985 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:18:24.988 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:18:24.990 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:18:24.991 +03:00 [INF] Hosting environment: Production
2025-07-15 17:18:24.993 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:18:26.107 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:18:26.158 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:18:26.171 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:18:26.174 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 70.338ms
2025-07-15 17:18:28.279 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=84WOIjjkOLFoL2frQnCSyg - null null
2025-07-15 17:18:28.287 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:18:28.326 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:18:28.383 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:18:32.234 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:18:32.240 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:18:32.242 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.7822ms
2025-07-15 17:18:32.246 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:18:32.249 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:18:32.250 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:18:32.267 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:18:32.670 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:18:32.678 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:18:32.680 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:18:32.682 +03:00 [INF] Initializing hub connection...
2025-07-15 17:18:34.798 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2121.8313ms.
2025-07-15 17:18:34.808 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:18:34.826 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2554.5654ms
2025-07-15 17:18:34.843 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:18:34.845 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2598.9373ms
2025-07-15 17:18:34.863 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:18:34.883 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:18:34.890 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:18:34.917 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 54.3473ms
2025-07-15 17:18:36.973 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=UN_9bhxrwJ4mGoQsmebl1g - null null
2025-07-15 17:18:36.994 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:18:38.658 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:18:38.662 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:18:38.663 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.4103ms
2025-07-15 17:18:38.665 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:18:38.672 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:18:38.675 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:18:38.677 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:18:38.686 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:18:38.688 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:18:38.691 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:18:38.692 +03:00 [INF] Initializing hub connection...
2025-07-15 17:18:40.724 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:18:40.735 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:18:40.739 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:18:40.766 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2077.8611ms.
2025-07-15 17:18:40.770 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 46.5299ms
2025-07-15 17:18:40.773 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:18:40.779 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2099.8787ms
2025-07-15 17:18:40.781 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:18:40.782 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2116.8858ms
2025-07-15 17:18:42.801 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=6yRv2etRaBGgHc2WmupD2g - null null
2025-07-15 17:18:42.813 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:18:42.826 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:18:44.183 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:18:44.187 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:18:44.190 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.8693ms
2025-07-15 17:18:44.192 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:18:44.197 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:18:44.198 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:18:44.200 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:18:44.213 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:18:44.215 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:18:44.216 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:18:44.217 +03:00 [INF] Initializing hub connection...
2025-07-15 17:18:46.256 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:18:46.271 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:18:46.273 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:18:46.322 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 66.2464ms
2025-07-15 17:18:46.324 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2109.3423ms.
2025-07-15 17:18:46.336 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:18:46.341 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2138.986ms
2025-07-15 17:18:46.346 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:18:46.348 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2156.8427ms
2025-07-15 17:18:48.351 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=xLz0CqqsniAYI6Jing58Yg - null null
2025-07-15 17:18:48.365 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:18:48.383 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:19:36.588 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:19:36.638 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:19:36.645 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:19:36.650 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:19:36.653 +03:00 [INF] Initializing hub connection...
2025-07-15 17:19:38.719 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:19:38.830 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:19:38.838 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:19:38.843 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:19:38.845 +03:00 [INF] Hosting environment: Production
2025-07-15 17:19:38.847 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:19:39.063 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:19:39.103 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:19:39.129 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:19:39.171 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 115.4679ms
2025-07-15 17:19:41.331 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=MDrP9794IducZzNZMbKlxA - null null
2025-07-15 17:19:41.338 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:19:41.396 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:19:41.474 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:20:51.639 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:20:51.647 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:20:51.650 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.6643ms
2025-07-15 17:20:51.654 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:20:51.660 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:20:51.662 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:20:51.670 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:20:52.104 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:20:52.110 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:20:52.112 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:20:52.113 +03:00 [INF] Initializing hub connection...
2025-07-15 17:20:54.172 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:20:54.175 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:20:54.176 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:20:54.200 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 27.6293ms
2025-07-15 17:20:54.207 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2093.6196ms.
2025-07-15 17:20:54.226 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:20:54.260 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2583.9402ms
2025-07-15 17:20:54.263 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:20:54.264 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2609.735ms
2025-07-15 17:20:56.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=hZrYB08UivQF8zVPY7nL4g - null null
2025-07-15 17:20:56.236 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:21:08.777 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:21:08.782 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:21:08.783 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.981ms
2025-07-15 17:21:08.786 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:21:08.792 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:21:08.794 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:21:08.796 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:21:08.808 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:21:08.809 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:21:08.811 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:21:08.813 +03:00 [INF] Initializing hub connection...
2025-07-15 17:21:10.872 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:21:10.874 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2064.7211ms.
2025-07-15 17:21:10.913 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:21:10.918 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:21:10.922 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:21:10.951 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2151.3966ms
2025-07-15 17:21:10.952 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.0899ms
2025-07-15 17:21:10.954 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:21:10.961 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2174.3996ms
2025-07-15 17:21:13.012 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=zV6XP5KTjKSIYZzPE2YW-g - null null
2025-07-15 17:21:13.021 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:21:13.034 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:21:55.384 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:21:55.388 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:21:55.390 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.2337ms
2025-07-15 17:21:55.391 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:21:55.398 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:21:55.400 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:21:55.402 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:21:55.412 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:21:55.415 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:21:55.418 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:21:55.419 +03:00 [INF] Initializing hub connection...
2025-07-15 17:21:58.958 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3543.505ms.
2025-07-15 17:21:59.318 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:22:00.099 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:22:01.881 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:22:03.415 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 8010.71ms
2025-07-15 17:22:04.220 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:22:27.721 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:22:42.187 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42868.3328ms
2025-07-15 17:22:49.887 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 54495.0009ms
2025-07-15 17:22:27.723 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=TrIWPrF_NIcsedPLiO4iKg - null null
2025-07-15 17:22:52.333 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 17:22:52.333 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 17:22:52.342 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:22:52.343 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=hZrYB08UivQF8zVPY7nL4g - 101 null null 116111.7206ms
2025-07-15 17:22:52.355 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:22:52.344 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=zV6XP5KTjKSIYZzPE2YW-g - 101 null null 99333.3817ms
2025-07-15 17:23:29.743 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:23:29.747 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:23:29.748 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 4.9995ms
2025-07-15 17:23:29.751 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:23:29.757 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:23:29.758 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:23:29.761 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:23:29.770 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:23:29.773 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:23:29.775 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:23:29.777 +03:00 [INF] Initializing hub connection...
2025-07-15 17:24:05.891 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:24:05.984 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 36211.7518ms.
2025-07-15 17:24:06.024 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 17:24:06.023 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 17:24:06.064 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:24:06.066 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:24:06.069 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:24:06.067 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=MDrP9794IducZzNZMbKlxA - 101 null null 264736.4211ms
2025-07-15 17:24:06.068 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=TrIWPrF_NIcsedPLiO4iKg - 101 null null 98648.1239ms
2025-07-15 17:24:06.091 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 36327.8589ms
2025-07-15 17:24:06.093 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 207.5699ms
2025-07-15 17:24:06.102 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:24:06.106 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 36355.012ms
2025-07-15 17:24:08.139 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Pap9W6GNYcr1gF4HNtFZhQ - null null
2025-07-15 17:24:08.146 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:24:36.378 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:24:36.478 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:24:36.492 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:24:36.504 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:24:36.509 +03:00 [INF] Initializing hub connection...
2025-07-15 17:24:39.875 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:24:40.061 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:24:40.071 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:24:40.081 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:24:40.085 +03:00 [INF] Hosting environment: Production
2025-07-15 17:24:40.089 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:24:40.316 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:24:40.374 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:24:40.393 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:24:40.397 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 91.1702ms
2025-07-15 17:24:42.562 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=1bInnKcM82fQyGIZno8evA - null null
2025-07-15 17:24:42.579 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:24:42.657 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:24:42.745 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:24:58.764 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:24:58.783 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:24:58.795 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 31.5828ms
2025-07-15 17:24:58.803 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:24:58.809 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:24:58.811 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:24:58.825 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:24:59.378 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:24:59.388 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:24:59.390 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:24:59.392 +03:00 [INF] Initializing hub connection...
2025-07-15 17:25:41.752 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:25:41.827 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:25:41.836 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:25:41.850 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:25:41.855 +03:00 [INF] Initializing hub connection...
2025-07-15 17:25:44.049 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:25:44.139 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:25:44.143 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:25:44.148 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:25:44.150 +03:00 [INF] Hosting environment: Production
2025-07-15 17:25:44.152 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:25:44.381 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:25:44.418 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:25:44.443 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:25:44.448 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 73.4443ms
2025-07-15 17:25:46.607 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=thbAG3T99mHWrUY9uj9DhA - null null
2025-07-15 17:25:46.616 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:25:46.705 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:25:46.785 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:25:51.457 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:25:51.463 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:25:51.468 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.6692ms
2025-07-15 17:25:51.472 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:25:51.477 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:25:51.478 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:25:51.497 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:25:51.910 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:25:51.917 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:25:51.918 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:25:51.919 +03:00 [INF] Initializing hub connection...
2025-07-15 17:26:00.766 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:26:00.771 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:26:00.772 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 8855.976ms.
2025-07-15 17:26:00.773 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:26:00.808 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 56.3318ms
2025-07-15 17:26:00.814 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:26:00.853 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 9347.7108ms
2025-07-15 17:26:00.857 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:26:00.861 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 9388.8464ms
2025-07-15 17:26:02.853 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=GFx7EGgQ8R00vc0okiU8Vw - null null
2025-07-15 17:26:02.860 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:26:07.183 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:26:07.187 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:26:07.189 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.4883ms
2025-07-15 17:26:07.193 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:26:07.199 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:26:07.200 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:26:07.201 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:26:07.214 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:26:07.216 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:26:07.218 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:26:07.219 +03:00 [INF] Initializing hub connection...
2025-07-15 17:26:09.281 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2064.7323ms.
2025-07-15 17:26:09.281 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:26:09.331 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:26:09.337 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:26:09.339 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2134.1971ms
2025-07-15 17:26:09.339 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:26:09.348 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:26:09.368 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 86.4614ms
2025-07-15 17:26:09.369 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2176.1743ms
2025-07-15 17:26:11.398 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=EXdf5gVTuO1VD29j0fCVdA - null null
2025-07-15 17:26:11.407 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:26:11.417 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:29:58.481 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:29:58.559 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:29:58.569 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:29:58.582 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:29:58.586 +03:00 [INF] Initializing hub connection...
2025-07-15 17:30:01.287 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:30:01.491 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:30:01.499 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:30:01.513 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:30:01.519 +03:00 [INF] Hosting environment: Production
2025-07-15 17:30:01.523 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:30:01.710 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:30:01.824 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:30:01.893 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:30:01.906 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 224.3757ms
2025-07-15 17:30:04.301 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=5K6Lfi49CZ-0BNxVQQy-Aw - null null
2025-07-15 17:30:04.309 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:30:04.395 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:30:04.482 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:30:13.202 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:30:13.220 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:30:13.222 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 20.3675ms
2025-07-15 17:30:13.226 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:30:13.232 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:30:13.235 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:30:13.243 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:30:13.751 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:30:13.757 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:30:13.759 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:30:13.760 +03:00 [INF] Initializing hub connection...
2025-07-15 17:30:15.844 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:30:15.852 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:30:15.854 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:30:15.881 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 36.9502ms
2025-07-15 17:30:15.887 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2125.7406ms.
2025-07-15 17:30:15.898 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:30:15.920 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2672.3696ms
2025-07-15 17:30:15.924 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:30:15.926 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2699.0769ms
2025-07-15 17:30:17.926 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tkHpUHPBVoRo9K6TaWfGNw - null null
2025-07-15 17:30:17.933 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:30:19.010 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:30:19.015 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:30:19.017 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.9178ms
2025-07-15 17:30:19.020 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:30:19.026 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:30:19.027 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:30:19.029 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:30:19.044 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:30:19.047 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:30:19.049 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:30:19.051 +03:00 [INF] Initializing hub connection...
2025-07-15 17:30:21.114 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:30:21.120 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:30:21.123 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2076.5837ms.
2025-07-15 17:30:21.124 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:30:21.161 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:30:21.163 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 48.9406ms
2025-07-15 17:30:21.165 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2133.045ms
2025-07-15 17:30:21.172 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:30:21.173 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2153.8559ms
2025-07-15 17:30:23.211 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2TgIuzpUeZR_mCu0UIrRRg - null null
2025-07-15 17:30:23.220 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:30:23.231 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:34:12.730 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:34:12.824 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:34:12.835 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:34:12.843 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:34:12.846 +03:00 [INF] Initializing hub connection...
2025-07-15 17:34:15.381 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:34:15.466 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:34:15.471 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:34:15.475 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:34:15.477 +03:00 [INF] Hosting environment: Production
2025-07-15 17:34:15.479 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:34:15.908 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:34:15.946 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:34:15.969 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:34:15.974 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 72.3589ms
2025-07-15 17:34:18.118 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ehdcFCEEYxplNn4ryfYsJQ - null null
2025-07-15 17:34:18.129 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:34:18.242 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:34:18.319 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:34:25.382 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 17:34:25.383 +03:00 [INF] COM ports populated.
2025-07-15 17:34:25.413 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 17:34:25.415 +03:00 [INF] TabPage returned successfully.
2025-07-15 17:34:36.757 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:34:36.763 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:34:36.765 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 8.5748ms
2025-07-15 17:34:36.770 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:34:36.774 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:34:36.775 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:34:36.785 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:34:37.187 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:34:37.193 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:34:37.195 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:34:37.196 +03:00 [INF] Initializing hub connection...
2025-07-15 17:34:39.253 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:34:39.258 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:34:39.260 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:34:39.302 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 49.3911ms
2025-07-15 17:34:39.303 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2087.5133ms.
2025-07-15 17:34:39.327 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:34:39.371 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2580.1204ms
2025-07-15 17:34:39.373 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:34:39.374 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2604.6537ms
2025-07-15 17:34:41.351 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2uXXYjNmAn7J79FZZU3GTw - null null
2025-07-15 17:34:41.359 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:34:52.408 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/ - null null
2025-07-15 17:34:52.437 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/ - 301 0 null 29.1082ms
2025-07-15 17:34:52.451 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-15 17:34:52.557 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 105.3974ms
2025-07-15 17:34:52.600 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-15 17:34:52.600 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-15 17:34:52.602 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-15 17:34:52.602 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-15 17:34:52.602 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-15 17:34:52.618 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-15 17:34:52.624 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 21.945ms
2025-07-15 17:34:52.625 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 24.8671ms
2025-07-15 17:34:52.644 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-15 17:34:52.645 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-15 17:34:52.649 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 48.5341ms
2025-07-15 17:34:52.649 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 47.0594ms
2025-07-15 17:34:52.655 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-15 17:34:52.660 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 57.9593ms
2025-07-15 17:34:53.018 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-15 17:34:53.198 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 179.8783ms
2025-07-15 17:34:57.140 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugImageCapture - null null
2025-07-15 17:34:57.149 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode)'
2025-07-15 17:34:57.157 +03:00 [INF] Route matched with {action = "DebugImageCapture", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugImageCapture() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:34:57.175 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:34:57.180 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:34:57.183 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:34:57.185 +03:00 [INF] Initializing hub connection...
2025-07-15 17:34:59.728 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:35:00.330 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 3151.9741ms.
2025-07-15 17:35:00.388 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:35:00.402 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:35:00.403 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:35:00.427 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 705.5517ms
2025-07-15 17:35:00.429 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode) in 3266.5832ms
2025-07-15 17:35:00.435 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode)'
2025-07-15 17:35:00.436 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugImageCapture - 200 null application/json; charset=utf-8 3296.5117ms
2025-07-15 17:35:02.463 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=21aijoybjTToimEfs6W96g - null null
2025-07-15 17:35:02.469 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:35:02.485 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:35:14.797 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugImageCapture - null null
2025-07-15 17:35:14.801 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode)'
2025-07-15 17:35:14.803 +03:00 [INF] Route matched with {action = "DebugImageCapture", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] DebugImageCapture() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:35:14.816 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:35:14.818 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:35:14.820 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:35:14.821 +03:00 [INF] Initializing hub connection...
2025-07-15 17:35:16.914 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:35:16.917 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:35:16.919 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:35:16.939 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 25.454ms
2025-07-15 17:35:17.860 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 3041.6696ms.
2025-07-15 17:35:17.863 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:35:17.865 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode) in 3059.8888ms
2025-07-15 17:35:17.867 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.DebugImageCapture (Mis.Agent.Barcode)'
2025-07-15 17:35:17.868 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/DebugImageCapture - 200 null application/json; charset=utf-8 3070.7247ms
2025-07-15 17:35:18.968 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=kSAOURa0BLQt2rLoP6BrFA - null null
2025-07-15 17:35:18.973 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:35:18.985 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:35:25.146 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/TestBarcodeReaderTypes - null null
2025-07-15 17:35:25.155 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode)'
2025-07-15 17:35:25.157 +03:00 [INF] Route matched with {action = "TestBarcodeReaderTypes", controller = "Barcode"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult TestBarcodeReaderTypes() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:35:25.168 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:35:25.175 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 4.7841ms.
2025-07-15 17:35:25.180 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType5`4[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType4`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]][], Mis.Agent.Barcode, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:35:25.196 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode) in 35.9425ms
2025-07-15 17:35:25.203 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.TestBarcodeReaderTypes (Mis.Agent.Barcode)'
2025-07-15 17:35:25.206 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/TestBarcodeReaderTypes - 200 null application/json; charset=utf-8 60.2617ms
2025-07-15 17:35:48.192 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Barcode/SetBarcodeReaderType - application/json 15
2025-07-15 17:35:48.198 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:35:48.199 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode)'
2025-07-15 17:35:48.209 +03:00 [INF] Route matched with {action = "SetBarcodeReaderType", controller = "Barcode"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetBarcodeReaderType(System.String) on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:35:48.260 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:35:48.264 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 0.7925ms.
2025-07-15 17:35:48.267 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType6`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:35:48.272 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode) in 58.3915ms
2025-07-15 17:35:48.274 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.SetBarcodeReaderType (Mis.Agent.Barcode)'
2025-07-15 17:35:48.277 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Barcode/SetBarcodeReaderType - 200 null application/json; charset=utf-8 84.7501ms
2025-07-15 17:36:05.163 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/Barcode/TestSpecificReaderType/Honeywell1950 - null null
2025-07-15 17:36:05.169 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.TestSpecificReaderType (Mis.Agent.Barcode)'
2025-07-15 17:36:05.181 +03:00 [INF] Route matched with {action = "TestSpecificReaderType", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestSpecificReaderType(System.String) on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:36:05.197 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.TestSpecificReaderType (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:36:05.201 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 17:36:05.205 +03:00 [INF] Capture image mode set to: true
2025-07-15 17:36:05.207 +03:00 [INF] Initializing hub connection...
2025-07-15 17:36:07.270 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:36:07.288 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:36:07.294 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:36:07.320 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 50.2181ms
2025-07-15 17:36:08.249 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.TestSpecificReaderType (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 3049.9352ms.
2025-07-15 17:36:08.254 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType7`4[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 17:36:08.262 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.TestSpecificReaderType (Mis.Agent.Barcode) in 3077.1924ms
2025-07-15 17:36:08.265 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.TestSpecificReaderType (Mis.Agent.Barcode)'
2025-07-15 17:36:08.268 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/Barcode/TestSpecificReaderType/Honeywell1950 - 200 null application/json; charset=utf-8 3104.8716ms
2025-07-15 17:36:09.376 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FGWotM8n77aCNZNubxPyUw - null null
2025-07-15 17:36:09.382 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:36:09.399 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:41:20.611 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:41:20.698 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 17:41:20.713 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 17:41:20.725 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:41:20.730 +03:00 [INF] Initializing hub connection...
2025-07-15 17:41:23.621 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:41:23.713 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:41:23.718 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:41:23.726 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:41:23.729 +03:00 [INF] Hosting environment: Production
2025-07-15 17:41:23.731 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:41:23.987 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:41:24.036 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:41:24.070 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:41:24.078 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 99.2738ms
2025-07-15 17:41:25.490 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 17:41:25.503 +03:00 [INF] COM ports populated.
2025-07-15 17:41:26.270 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=m8uMti6TUMpWPvLuV_koRQ - null null
2025-07-15 17:41:26.286 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:41:26.419 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 17:41:26.451 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:41:26.653 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 17:41:26.655 +03:00 [INF] TabPage returned successfully.
2025-07-15 17:41:36.564 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:41:36.590 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:41:36.594 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:41:36.598 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:41:36.599 +03:00 [INF] Initializing hub connection...
2025-07-15 17:41:37.551 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:41:37.580 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:41:37.583 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:41:37.586 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:41:37.586 +03:00 [INF] Hosting environment: Production
2025-07-15 17:41:37.587 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:41:38.724 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:41:38.748 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:41:38.758 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:41:38.763 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 40.5725ms
2025-07-15 17:41:40.807 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=0gPn04NPCXPzWo-hwHpxpw - null null
2025-07-15 17:41:40.817 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:41:40.845 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:41:40.891 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:41:48.080 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 17:41:48.081 +03:00 [INF] COM ports populated.
2025-07-15 17:41:48.083 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 17:41:48.085 +03:00 [INF] TabPage returned successfully.
2025-07-15 17:41:54.473 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:41:54.479 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:41:54.480 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.6898ms
2025-07-15 17:41:54.484 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:41:54.486 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:41:54.487 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:41:54.492 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:42:13.128 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:42:13.172 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:42:13.178 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:42:13.182 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:42:13.184 +03:00 [INF] Initializing hub connection...
2025-07-15 17:42:15.449 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:42:15.568 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:42:15.572 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:42:15.578 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:42:15.581 +03:00 [INF] Hosting environment: Production
2025-07-15 17:42:15.584 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:42:16.119 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:42:16.177 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:42:16.208 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:42:16.215 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 106.2175ms
2025-07-15 17:42:18.397 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=_7r-kxwnFicDlhMWDLWS5w - null null
2025-07-15 17:42:18.404 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:42:18.507 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:42:18.615 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:42:24.706 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:42:24.714 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:42:24.715 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.1149ms
2025-07-15 17:42:24.719 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:42:24.722 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:42:24.723 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:42:24.740 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:42:25.130 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:42:52.010 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:42:52.086 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:42:52.096 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:42:52.105 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:42:52.107 +03:00 [INF] Initializing hub connection...
2025-07-15 17:42:55.886 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:42:56.260 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:42:56.301 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:42:56.314 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:42:56.318 +03:00 [INF] Hosting environment: Production
2025-07-15 17:42:56.322 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:42:56.708 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:42:56.773 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:42:56.808 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:42:56.818 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 124.7923ms
2025-07-15 17:42:59.067 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=5dN5tdcQbsad_DspbYteuw - null null
2025-07-15 17:42:59.085 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:42:59.324 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:42:59.508 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:43:00.410 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:43:00.438 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:43:00.448 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 38.269ms
2025-07-15 17:43:00.469 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:43:00.484 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:43:00.491 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:43:00.600 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:43:01.558 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 17:45:07.305 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 17:45:07.387 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 17:45:07.401 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 17:45:07.412 +03:00 [INF] Capture image mode set to: false
2025-07-15 17:45:07.420 +03:00 [INF] Initializing hub connection...
2025-07-15 17:45:10.222 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 17:45:10.361 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 17:45:10.368 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 17:45:10.376 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 17:45:10.380 +03:00 [INF] Hosting environment: Production
2025-07-15 17:45:10.382 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 17:45:10.519 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 17:45:10.572 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 17:45:10.606 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 17:45:10.612 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 103.5674ms
2025-07-15 17:45:12.885 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=7hNje26Pzki86r6BhEnN2g - null null
2025-07-15 17:45:12.893 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 17:45:12.967 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 17:45:13.060 +03:00 [INF] Barcode initialized successfully.
2025-07-15 17:45:27.076 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:45:27.083 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:45:27.086 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.3778ms
2025-07-15 17:45:27.091 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 17:45:27.095 +03:00 [INF] CORS policy execution successful.
2025-07-15 17:45:27.097 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 17:45:27.116 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 17:45:27.581 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
