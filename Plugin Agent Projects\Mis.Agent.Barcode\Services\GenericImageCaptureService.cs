using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Mis.Agent.Barcode.Services
{
    public class GenericImageCaptureService
    {
        public async Task<string> CaptureImageAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    Console.WriteLine("[DEBUG] Starting generic image capture...");

                    // Try to capture from clipboard first (if user manually copies an image)
                    string clipboardImage = TryGetImageFromClipboard();
                    if (!string.IsNullOrEmpty(clipboardImage))
                    {
                        Console.WriteLine("[DEBUG] ✓ Image captured from clipboard");
                        return clipboardImage;
                    }

                    // Try to use OpenFileDialog as a fallback for manual image selection
                    string fileImage = TryGetImageFromFile();
                    if (!string.IsNullOrEmpty(fileImage))
                    {
                        Console.WriteLine("[DEBUG] ✓ Image selected from file");
                        return fileImage;
                    }

                    Console.WriteLine("[DEBUG] No image capture method succeeded");
                    return null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DEBUG] Generic capture exception: {ex.Message}");
                    return null;
                }
            });
        }

        private string TryGetImageFromClipboard()
        {
            try
            {
                if (Clipboard.ContainsImage())
                {
                    using (Image image = Clipboard.GetImage())
                    {
                        return ConvertImageToBase64(image);
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Clipboard capture failed: {ex.Message}");
                return null;
            }
        }

        private string TryGetImageFromFile()
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Title = "Select Image from Barcode Reader";
                    openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff|All Files|*.*";
                    openFileDialog.Multiselect = false;

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        string filePath = openFileDialog.FileName;
                        if (File.Exists(filePath))
                        {
                            byte[] imageBytes = File.ReadAllBytes(filePath);
                            return Convert.ToBase64String(imageBytes);
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] File selection failed: {ex.Message}");
                return null;
            }
        }

        private string ConvertImageToBase64(Image image)
        {
            try
            {
                using (var ms = new MemoryStream())
                {
                    image.Save(ms, ImageFormat.Png);
                    byte[] imageBytes = ms.ToArray();
                    return Convert.ToBase64String(imageBytes);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Image conversion failed: {ex.Message}");
                return null;
            }
        }

        public async Task<string> CaptureImageFromDeviceAsync(string deviceName = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    Console.WriteLine($"[DEBUG] Attempting to capture from device: {deviceName ?? "default"}");
                    
                    // This is a placeholder for device-specific capture
                    // In a real implementation, you would use device-specific APIs
                    // For now, we'll fall back to the generic methods
                    
                    return TryGetImageFromClipboard() ?? TryGetImageFromFile();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DEBUG] Device capture failed: {ex.Message}");
                    return null;
                }
            });
        }
    }
}
