using Mis.Agent.Barcode.Services;
using Xunit;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode.Tests
{
    public class TwainImageCaptureServiceTests
    {
        [Fact]
        public async Task CaptureImageAsync_ShouldReturnStringOrNull()
        {
            // Arrange
            var service = new TwainImageCaptureService();

            // Act
            var result = await service.CaptureImageAsync();

            // Assert
            // Result can be null if no TWAIN devices are available
            // or a Base64 string if capture is successful
            Assert.True(result == null || !string.IsNullOrEmpty(result));
        }

        [Fact]
        public void TwainImageCaptureService_ShouldBeInstantiable()
        {
            // Arrange & Act
            var service = new TwainImageCaptureService();

            // Assert
            Assert.NotNull(service);
        }
    }
}
